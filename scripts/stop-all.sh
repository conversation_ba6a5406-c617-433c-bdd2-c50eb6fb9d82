#!/bin/bash

# 昆明花卉拍卖系统 - 停止所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_service() {
    local service=$1
    local pid_file="logs/$service.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            log_info "停止 $service (PID: $pid)..."
            kill $pid
            
            # 等待进程结束
            local count=0
            while ps -p $pid > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                log_warn "强制停止 $service..."
                kill -9 $pid
            fi
            
            log_info "$service 已停止"
        else
            log_warn "$service 进程不存在 (PID: $pid)"
        fi
        
        # 删除PID文件
        rm -f "$pid_file"
    else
        log_warn "$service PID文件不存在"
    fi
}

# 主函数
main() {
    log_info "开始停止昆明花卉拍卖系统所有服务..."
    
    # 服务列表（按停止顺序）
    services=(
        "gateway"
        "upload-service"
        "product-service"
        "user-service"
        "auth-service"
    )
    
    # 停止所有服务
    for service in "${services[@]}"; do
        stop_service "$service"
    done
    
    # 清理可能残留的进程
    log_info "清理残留进程..."
    
    # 查找并杀死可能的残留进程
    pkill -f "cmd/.*-service/main.go" 2>/dev/null || true
    pkill -f "cmd/gateway/main.go" 2>/dev/null || true
    
    log_info "所有服务已停止"
    
    # 显示端口占用情况
    log_info "检查端口占用情况："
    for port in 8080 8081 8082 8083 8084; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warn "端口 $port 仍被占用"
            lsof -Pi :$port -sTCP:LISTEN
        fi
    done
}

# 执行主函数
main "$@"
