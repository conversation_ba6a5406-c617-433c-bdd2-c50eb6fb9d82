package main

import (
	"flag"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/auth"
	"github.com/putonghao/flower-auction/internal/shared/config"
	"github.com/putonghao/flower-auction/internal/shared/database"
	"github.com/putonghao/flower-auction/internal/shared/middleware"
	"github.com/putonghao/flower-auction/internal/shared/response"
)

func main() {
	// 解析命令行参数
	var configPath = flag.String("config", "configs/auth-service.yaml", "配置文件路径")
	flag.Parse()

	// 初始化配置
	if err := config.Init(*configPath); err != nil {
		log.Fatalf("Failed to initialize config: %v", err)
	}

	// 初始化数据库
	if err := database.InitDatabases(); err != nil {
		log.Fatalf("Failed to initialize databases: %v", err)
	}
	defer database.Close()

	// 创建认证服务
	authService := auth.NewService(nil) // 这里需要注入用户服务，暂时为nil

	// 创建HTTP处理器
	authHandler := auth.NewHandler(authService)

	// 创建认证中间件
	authMiddleware := auth.NewMiddleware(authService)

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORS())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"service": "auth-service",
			"status":  "healthy",
			"time":    time.Now(),
		})
	})

	// 注册认证路由
	authHandler.RegisterRoutes(r)

	// 受保护的路由示例
	protected := r.Group("/api/v1/protected")
	protected.Use(authMiddleware.JWTAuth())
	{
		protected.GET("/test", func(c *gin.Context) {
			userID, username, userType, roles, ok := auth.GetCurrentUser(c)
			if !ok {
				response.Unauthorized(c, "未授权")
				return
			}

			response.Success(c, gin.H{
				"message":  "这是受保护的接口",
				"userId":   userID,
				"username": username,
				"userType": userType,
				"roles":    roles,
			})
		})
	}

	// 启动服务器
	serverConfig := config.GetServer()
	server := &http.Server{
		Addr:         ":" + string(rune(serverConfig.Port)),
		Handler:      r,
		ReadTimeout:  time.Duration(serverConfig.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(serverConfig.WriteTimeout) * time.Second,
	}

	log.Printf("Auth service starting on port %d", serverConfig.Port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}
