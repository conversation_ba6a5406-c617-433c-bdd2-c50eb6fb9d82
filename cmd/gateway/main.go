package main

import (
	"flag"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/shared/config"
	"github.com/putonghao/flower-auction/internal/shared/middleware"
	"github.com/putonghao/flower-auction/internal/shared/response"
)

// ServiceConfig 服务配置
type ServiceConfig struct {
	Name string
	URL  string
}

// Gateway API网关
type Gateway struct {
	services map[string]*ServiceConfig
}

// NewGateway 创建API网关
func NewGateway() *Gateway {
	return &Gateway{
		services: map[string]*ServiceConfig{
			"auth": {
				Name: "auth-service",
				URL:  "http://localhost:8081",
			},
			"user": {
				Name: "user-service",
				URL:  "http://localhost:8082",
			},
			"product": {
				Name: "product-service",
				URL:  "http://localhost:8083",
			},
			"upload": {
				Name: "upload-service",
				URL:  "http://localhost:8084",
			},
		},
	}
}

// ProxyHandler 代理处理器
func (g *Gateway) ProxyHandler(serviceName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		service, exists := g.services[serviceName]
		if !exists {
			response.NotFound(c, "服务不存在")
			return
		}

		// 解析目标URL
		target, err := url.Parse(service.URL)
		if err != nil {
			response.InternalServerError(c, "服务配置错误")
			return
		}

		// 创建反向代理
		proxy := httputil.NewSingleHostReverseProxy(target)

		// 修改请求路径
		originalPath := c.Request.URL.Path
		c.Request.URL.Path = strings.TrimPrefix(originalPath, "/api/v1/"+serviceName)
		if c.Request.URL.Path == "" {
			c.Request.URL.Path = "/"
		}

		// 设置请求头
		c.Request.Header.Set("X-Forwarded-Host", c.Request.Host)
		c.Request.Header.Set("X-Origin-Host", target.Host)

		// 执行代理
		proxy.ServeHTTP(c.Writer, c.Request)
	}
}

// RegisterRoutes 注册路由
func (g *Gateway) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api/v1")
	{
		// 认证服务路由
		auth := api.Group("/auth")
		auth.Any("/*path", g.ProxyHandler("auth"))

		// 用户服务路由
		user := api.Group("/user")
		user.Any("/*path", g.ProxyHandler("user"))

		// 商品服务路由
		product := api.Group("/product")
		product.Any("/*path", g.ProxyHandler("product"))

		// 上传服务路由
		upload := api.Group("/upload")
		upload.Any("/*path", g.ProxyHandler("upload"))
	}
}

func main() {
	// 解析命令行参数
	var configPath = flag.String("config", "configs/gateway.yaml", "配置文件路径")
	flag.Parse()

	// 初始化配置
	if err := config.Init(*configPath); err != nil {
		log.Fatalf("Failed to initialize config: %v", err)
	}

	// 创建API网关
	gateway := NewGateway()

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.CORS())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		response.Success(c, gin.H{
			"service": "api-gateway",
			"status":  "healthy",
			"time":    time.Now(),
		})
	})

	// 服务发现接口
	r.GET("/services", func(c *gin.Context) {
		services := make([]gin.H, 0, len(gateway.services))
		for name, service := range gateway.services {
			services = append(services, gin.H{
				"name": name,
				"url":  service.URL,
			})
		}
		response.Success(c, services)
	})

	// 注册代理路由
	gateway.RegisterRoutes(r)

	// 启动服务器
	serverConfig := config.GetServer()
	server := &http.Server{
		Addr:         ":" + string(rune(serverConfig.Port)),
		Handler:      r,
		ReadTimeout:  time.Duration(serverConfig.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(serverConfig.WriteTimeout) * time.Second,
	}

	log.Printf("API Gateway starting on port %d", serverConfig.Port)
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}
