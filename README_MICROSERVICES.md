# 昆明花卉拍卖系统 - 微服务架构

## 🏗️ 架构概览

本项目采用微服务架构，将业务功能拆分为独立的服务，每个服务都有自己的数据库、API和部署单元。

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │  Mobile App     │    │  Admin Panel    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      API Gateway         │
                    │     (Port: 8080)         │
                    └─────────────┬─────────────┘
                                 │
        ┌────────────┬────────────┼────────────┬────────────┐
        │            │            │            │            │
   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐
   │ Auth   │   │ User   │   │Product │   │Upload  │   │  Log   │
   │Service │   │Service │   │Service │   │Service │   │Service │
   │ :8081  │   │ :8082  │   │ :8083  │   │ :8084  │   │ :8085  │
   └────┬───┘   └────┬───┘   └────┬───┘   └────┬───┘   └────┬───┘
        │            │            │            │            │
   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐   ┌────▼───┐
   │ User   │   │ User   │   │Product │   │  OSS   │   │  Log   │
   │   DB   │   │   DB   │   │   DB   │   │Storage │   │   DB   │
   └────────┘   └────────┘   └────────┘   └────────┘   └────────┘
```

## 📦 服务列表

### 1. API Gateway (端口: 8080)
- **功能**: 统一入口、路由转发、负载均衡
- **路径**: `cmd/gateway/`
- **配置**: `configs/gateway.yaml`

### 2. Auth Service (端口: 8081)
- **功能**: 用户认证、JWT令牌管理
- **路径**: `cmd/auth-service/`
- **配置**: `configs/auth-service.yaml`
- **数据库**: `flower_auction_user`

### 3. User Service (端口: 8082)
- **功能**: 用户管理、角色权限
- **路径**: `cmd/user-service/`
- **配置**: `configs/user-service.yaml`
- **数据库**: `flower_auction_user`

### 4. Product Service (端口: 8083)
- **功能**: 商品管理、分类管理
- **路径**: `cmd/product-service/`
- **配置**: `configs/product-service.yaml`
- **数据库**: `flower_auction_product`

### 5. Upload Service (端口: 8084)
- **功能**: 文件上传、OSS集成
- **路径**: `cmd/upload-service/`
- **配置**: `configs/upload-service.yaml`
- **存储**: 阿里云OSS / 本地存储

### 6. Log Service (端口: 8085)
- **功能**: 日志收集、分析统计
- **路径**: `cmd/log-service/`
- **配置**: `configs/log-service.yaml`
- **数据库**: `flower_auction_log`

## 🚀 快速开始

### 环境要求
- Go 1.19+
- MySQL 8.0+
- Redis 6.0+
- 阿里云OSS (可选)

### 1. 克隆项目
```bash
git clone https://github.com/putonghao/flower-auction.git
cd flower-auction
```

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 配置数据库
```sql
-- 创建数据库
CREATE DATABASE flower_auction_user CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE flower_auction_product CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE flower_auction_log CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 修改配置文件
编辑 `configs/` 目录下的配置文件，修改数据库连接信息。

### 5. 启动所有服务
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 启动所有服务
./scripts/start-all.sh
```

### 6. 验证服务
```bash
# 检查API网关
curl http://localhost:8080/health

# 检查各个服务
curl http://localhost:8081/health  # 认证服务
curl http://localhost:8082/health  # 用户服务
```

## 🔧 开发指南

### 项目结构
```
flower-auction/
├── cmd/                    # 服务启动入口
│   ├── auth-service/       # 认证服务
│   ├── user-service/       # 用户服务
│   ├── product-service/    # 商品服务
│   ├── upload-service/     # 上传服务
│   ├── log-service/        # 日志服务
│   └── gateway/            # API网关
├── internal/               # 内部模块
│   ├── auth/               # 认证模块
│   ├── user/               # 用户模块
│   ├── product/            # 商品模块
│   ├── upload/             # 上传模块
│   ├── log/                # 日志模块
│   └── shared/             # 公共模块
│       ├── database/       # 数据库连接
│       ├── config/         # 配置管理
│       ├── middleware/     # 公共中间件
│       ├── response/       # 统一响应
│       └── utils/          # 工具函数
├── configs/                # 配置文件
├── scripts/                # 脚本文件
├── api/                    # API文档
├── deployments/            # 部署配置
└── docs/                   # 文档
```

### 添加新服务

1. **创建服务目录**
```bash
mkdir -p cmd/new-service
mkdir -p internal/new-service
```

2. **实现服务模块**
```go
// internal/new-service/model.go
// internal/new-service/repository.go
// internal/new-service/service.go
// internal/new-service/handler.go
```

3. **创建启动入口**
```go
// cmd/new-service/main.go
```

4. **添加配置文件**
```yaml
# configs/new-service.yaml
```

5. **更新网关路由**
```go
// cmd/gateway/main.go
// 添加新服务的路由配置
```

### API设计规范

#### 路由规范
```
GET    /api/v1/{service}/{resource}           # 列表查询
GET    /api/v1/{service}/{resource}/{id}      # 详情查询
POST   /api/v1/{service}/{resource}           # 创建资源
PUT    /api/v1/{service}/{resource}/{id}      # 更新资源
DELETE /api/v1/{service}/{resource}/{id}      # 删除资源
```

#### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "详细错误信息"
}
```

## 🔐 认证授权

### JWT令牌
所有需要认证的接口都需要在请求头中携带JWT令牌：
```
Authorization: Bearer <token>
```

### 权限控制
基于RBAC模型，支持角色和权限的灵活配置。

## 📊 监控运维

### 健康检查
每个服务都提供健康检查接口：
```bash
curl http://localhost:{port}/health
```

### 日志查看
```bash
# 查看所有服务日志
tail -f logs/*.log

# 查看特定服务日志
tail -f logs/auth-service.log
```

### 服务管理
```bash
# 启动所有服务
./scripts/start-all.sh

# 停止所有服务
./scripts/stop-all.sh

# 重启特定服务
./scripts/restart-service.sh auth-service
```

## 🐳 Docker部署

### 构建镜像
```bash
# 构建所有服务镜像
docker-compose build

# 构建特定服务镜像
docker build -t flower-auction/auth-service -f deployments/docker/auth-service.Dockerfile .
```

### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📝 API文档

### Swagger文档
每个服务都提供Swagger文档：
- API网关: http://localhost:8080/swagger/index.html
- 认证服务: http://localhost:8081/swagger/index.html
- 用户服务: http://localhost:8082/swagger/index.html

### Postman集合
导入 `api/postman/` 目录下的Postman集合文件进行接口测试。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目地址: https://github.com/putonghao/flower-auction
- 问题反馈: https://github.com/putonghao/flower-auction/issues
- 邮箱: <EMAIL>
