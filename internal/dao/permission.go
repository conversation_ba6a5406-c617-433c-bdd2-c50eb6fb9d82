package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// PermissionDAO 权限数据访问接口
type PermissionDAO interface {
	// 权限管理
	CreatePermission(ctx context.Context, permission *model.Permission) error
	UpdatePermission(ctx context.Context, permission *model.Permission) error
	DeletePermission(ctx context.Context, id int64) error
	FindPermissionByID(ctx context.Context, id int64) (*model.Permission, error)
	FindPermissionByCode(ctx context.Context, code string) (*model.Permission, error)
	ListPermissions(ctx context.Context, module string, offset, limit int) ([]*model.Permission, error)
	CountPermissions(ctx context.Context, module string) (int64, error)

	// 角色权限管理
	AssignPermissionToRole(ctx context.Context, roleID, permissionID int64) error
	RemovePermissionFromRole(ctx context.Context, roleID, permissionID int64) error
	FindRolePermissions(ctx context.Context, roleID int64) ([]*model.Permission, error)
	FindPermissionRoles(ctx context.Context, permissionID int64) ([]*model.Role, error)
	FindRoleWithPermissions(ctx context.Context, roleID int64) (*model.RoleWithPermissions, error)

	// 用户权限查询
	FindUserPermissions(ctx context.Context, userID int64) ([]*model.UserPermission, error)
	CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error)
	FindUserPermissionsByModule(ctx context.Context, userID int64, module string) ([]*model.Permission, error)
}

// permissionDAO 权限数据访问实现
type permissionDAO struct {
	db *gorm.DB
}

// NewPermissionDAO 创建权限数据访问实例
func NewPermissionDAO() PermissionDAO {
	return &permissionDAO{
		db: GetUserDB(),
	}
}

// CreatePermission 创建权限
func (d *permissionDAO) CreatePermission(ctx context.Context, permission *model.Permission) error {
	return d.db.WithContext(ctx).Create(permission).Error
}

// UpdatePermission 更新权限
func (d *permissionDAO) UpdatePermission(ctx context.Context, permission *model.Permission) error {
	return d.db.WithContext(ctx).Save(permission).Error
}

// DeletePermission 删除权限
func (d *permissionDAO) DeletePermission(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 先删除角色权限关联
		if err := tx.Where("permission_id = ?", id).Delete(&model.RolePermission{}).Error; err != nil {
			return err
		}
		// 再删除权限
		return tx.Delete(&model.Permission{}, id).Error
	})
}

// FindPermissionByID 根据ID查找权限
func (d *permissionDAO) FindPermissionByID(ctx context.Context, id int64) (*model.Permission, error) {
	var permission model.Permission
	err := d.db.WithContext(ctx).First(&permission, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// FindPermissionByCode 根据代码查找权限
func (d *permissionDAO) FindPermissionByCode(ctx context.Context, code string) (*model.Permission, error) {
	var permission model.Permission
	err := d.db.WithContext(ctx).Where("code = ?", code).First(&permission).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &permission, nil
}

// ListPermissions 查询权限列表
func (d *permissionDAO) ListPermissions(ctx context.Context, module string, offset, limit int) ([]*model.Permission, error) {
	var permissions []*model.Permission
	query := d.db.WithContext(ctx)
	if module != "" {
		query = query.Where("module = ?", module)
	}
	err := query.Offset(offset).Limit(limit).Order("module ASC, created_at ASC").Find(&permissions).Error
	return permissions, err
}

// CountPermissions 统计权限数量
func (d *permissionDAO) CountPermissions(ctx context.Context, module string) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.Permission{})
	if module != "" {
		query = query.Where("module = ?", module)
	}
	err := query.Count(&count).Error
	return count, err
}

// AssignPermissionToRole 为角色分配权限
func (d *permissionDAO) AssignPermissionToRole(ctx context.Context, roleID, permissionID int64) error {
	// 检查是否已存在
	var count int64
	err := d.db.WithContext(ctx).Model(&model.RolePermission{}).
		Where("role_id = ? AND permission_id = ?", roleID, permissionID).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return nil // 已存在，不重复添加
	}

	rolePermission := &model.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
	}
	return d.db.WithContext(ctx).Create(rolePermission).Error
}

// RemovePermissionFromRole 从角色移除权限
func (d *permissionDAO) RemovePermissionFromRole(ctx context.Context, roleID, permissionID int64) error {
	return d.db.WithContext(ctx).Where("role_id = ? AND permission_id = ?", roleID, permissionID).
		Delete(&model.RolePermission{}).Error
}

// FindRolePermissions 查找角色的权限
func (d *permissionDAO) FindRolePermissions(ctx context.Context, roleID int64) ([]*model.Permission, error) {
	var permissions []*model.Permission
	err := d.db.WithContext(ctx).
		Select("p.*").
		Table("permission p").
		Joins("INNER JOIN role_permission rp ON p.id = rp.permission_id").
		Where("rp.role_id = ? AND p.status = 1", roleID).
		Order("p.module ASC, p.created_at ASC").
		Find(&permissions).Error
	return permissions, err
}

// FindPermissionRoles 查找权限的角色
func (d *permissionDAO) FindPermissionRoles(ctx context.Context, permissionID int64) ([]*model.Role, error) {
	var roles []*model.Role
	err := d.db.WithContext(ctx).
		Select("r.*").
		Table("role r").
		Joins("INNER JOIN role_permission rp ON r.id = rp.role_id").
		Where("rp.permission_id = ? AND r.status = 1", permissionID).
		Order("r.created_at ASC").
		Find(&roles).Error
	return roles, err
}

// FindRoleWithPermissions 查找角色及其权限
func (d *permissionDAO) FindRoleWithPermissions(ctx context.Context, roleID int64) (*model.RoleWithPermissions, error) {
	// 查找角色
	var role model.Role
	err := d.db.WithContext(ctx).First(&role, roleID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// 查找权限
	permissions, err := d.FindRolePermissions(ctx, roleID)
	if err != nil {
		return nil, err
	}

	// 转换权限切片
	permissionSlice := make([]model.Permission, len(permissions))
	for i, p := range permissions {
		permissionSlice[i] = *p
	}

	return &model.RoleWithPermissions{
		Role:        role,
		Permissions: permissionSlice,
	}, nil
}

// FindUserPermissions 查找用户权限
func (d *permissionDAO) FindUserPermissions(ctx context.Context, userID int64) ([]*model.UserPermission, error) {
	var userPermissions []*model.UserPermission
	err := d.db.WithContext(ctx).
		Select("u.id as user_id, u.username, r.id as role_id, r.name as role_name, p.id as permission_id, p.code as permission_code, p.module, p.action, p.resource").
		Table("user u").
		Joins("INNER JOIN user_role ur ON u.id = ur.user_id").
		Joins("INNER JOIN role r ON ur.role_id = r.id").
		Joins("INNER JOIN role_permission rp ON r.id = rp.role_id").
		Joins("INNER JOIN permission p ON rp.permission_id = p.id").
		Where("u.id = ? AND u.status = 1 AND r.status = 1 AND p.status = 1", userID).
		Order("p.module ASC, p.created_at ASC").
		Find(&userPermissions).Error
	return userPermissions, err
}

// CheckUserPermission 检查用户是否有指定权限
func (d *permissionDAO) CheckUserPermission(ctx context.Context, userID int64, permissionCode string) (bool, error) {
	var count int64
	err := d.db.WithContext(ctx).
		Table("user u").
		Joins("INNER JOIN user_role ur ON u.id = ur.user_id").
		Joins("INNER JOIN role r ON ur.role_id = r.id").
		Joins("INNER JOIN role_permission rp ON r.id = rp.role_id").
		Joins("INNER JOIN permission p ON rp.permission_id = p.id").
		Where("u.id = ? AND p.code = ? AND u.status = 1 AND r.status = 1 AND p.status = 1", userID, permissionCode).
		Count(&count).Error
	return count > 0, err
}

// FindUserPermissionsByModule 根据模块查找用户权限
func (d *permissionDAO) FindUserPermissionsByModule(ctx context.Context, userID int64, module string) ([]*model.Permission, error) {
	var permissions []*model.Permission
	err := d.db.WithContext(ctx).
		Select("DISTINCT p.*").
		Table("permission p").
		Joins("INNER JOIN role_permission rp ON p.id = rp.permission_id").
		Joins("INNER JOIN role r ON rp.role_id = r.id").
		Joins("INNER JOIN user_role ur ON r.id = ur.role_id").
		Joins("INNER JOIN user u ON ur.user_id = u.id").
		Where("u.id = ? AND p.module = ? AND u.status = 1 AND r.status = 1 AND p.status = 1", userID, module).
		Order("p.created_at ASC").
		Find(&permissions).Error
	return permissions, err
}
