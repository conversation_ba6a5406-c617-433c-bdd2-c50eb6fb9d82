package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// FinanceDAO 财务数据访问接口
type FinanceDAO interface {
	// 账户管理
	CreateAccount(ctx context.Context, account *model.Account) error
	UpdateAccount(ctx context.Context, account *model.Account) error
	FindAccountByID(ctx context.Context, id int64) (*model.Account, error)
	FindAccountByUserAndType(ctx context.Context, userID int64, accountType int8) (*model.Account, error)
	ListAccountsByUser(ctx context.Context, userID int64) ([]*model.Account, error)
	
	// 交易流水管理
	CreateTransaction(ctx context.Context, transaction *model.Transaction) error
	FindTransactionByID(ctx context.Context, id int64) (*model.Transaction, error)
	FindTransactionByNo(ctx context.Context, transactionNo string) (*model.Transaction, error)
	ListTransactionsByUser(ctx context.Context, userID int64, offset, limit int) ([]*model.Transaction, error)
	CountTransactionsByUser(ctx context.Context, userID int64) (int64, error)
	
	// 佣金管理
	CreateCommission(ctx context.Context, commission *model.Commission) error
	UpdateCommission(ctx context.Context, commission *model.Commission) error
	FindCommissionByID(ctx context.Context, id int64) (*model.Commission, error)
	ListCommissions(ctx context.Context, offset, limit int) ([]*model.Commission, error)
	CountCommissions(ctx context.Context) (int64, error)
	
	// 统计查询
	GetTotalIncomeByDateRange(ctx context.Context, startDate, endDate time.Time) (float64, error)
	GetTotalExpenseByDateRange(ctx context.Context, startDate, endDate time.Time) (float64, error)
	GetTransactionCountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error)
	GetUserTotalIncomeByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (float64, error)
	GetUserTotalExpenseByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (float64, error)
	GetUserTransactionCountByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (int64, error)
}

// financeDAO 财务数据访问实现
type financeDAO struct {
	db *gorm.DB
}

// NewFinanceDAO 创建财务数据访问实例
func NewFinanceDAO() FinanceDAO {
	return &financeDAO{
		db: GetFinanceDB(),
	}
}

// CreateAccount 创建账户
func (d *financeDAO) CreateAccount(ctx context.Context, account *model.Account) error {
	return d.db.WithContext(ctx).Create(account).Error
}

// UpdateAccount 更新账户
func (d *financeDAO) UpdateAccount(ctx context.Context, account *model.Account) error {
	return d.db.WithContext(ctx).Save(account).Error
}

// FindAccountByID 根据ID查找账户
func (d *financeDAO) FindAccountByID(ctx context.Context, id int64) (*model.Account, error) {
	var account model.Account
	err := d.db.WithContext(ctx).First(&account, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &account, nil
}

// FindAccountByUserAndType 根据用户ID和账户类型查找账户
func (d *financeDAO) FindAccountByUserAndType(ctx context.Context, userID int64, accountType int8) (*model.Account, error) {
	var account model.Account
	err := d.db.WithContext(ctx).
		Where("user_id = ? AND account_type = ?", userID, accountType).
		First(&account).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &account, nil
}

// ListAccountsByUser 查询用户的所有账户
func (d *financeDAO) ListAccountsByUser(ctx context.Context, userID int64) ([]*model.Account, error) {
	var accounts []*model.Account
	err := d.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Find(&accounts).Error
	return accounts, err
}

// CreateTransaction 创建交易记录
func (d *financeDAO) CreateTransaction(ctx context.Context, transaction *model.Transaction) error {
	// 根据用户ID进行分表
	tableName := getTransactionTableName(transaction.UserID)
	return d.db.WithContext(ctx).Table(tableName).Create(transaction).Error
}

// FindTransactionByID 根据ID查找交易记录
func (d *financeDAO) FindTransactionByID(ctx context.Context, id int64) (*model.Transaction, error) {
	// 需要遍历所有分表查找
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("transaction_%d", i)
		var transaction model.Transaction
		err := d.db.WithContext(ctx).Table(tableName).First(&transaction, id).Error
		if err == nil {
			return &transaction, nil
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}
	return nil, nil
}

// FindTransactionByNo 根据交易号查找交易记录
func (d *financeDAO) FindTransactionByNo(ctx context.Context, transactionNo string) (*model.Transaction, error) {
	// 需要遍历所有分表查找
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("transaction_%d", i)
		var transaction model.Transaction
		err := d.db.WithContext(ctx).Table(tableName).
			Where("transaction_no = ?", transactionNo).
			First(&transaction).Error
		if err == nil {
			return &transaction, nil
		}
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}
	return nil, nil
}

// ListTransactionsByUser 查询用户的交易记录
func (d *financeDAO) ListTransactionsByUser(ctx context.Context, userID int64, offset, limit int) ([]*model.Transaction, error) {
	tableName := getTransactionTableName(userID)
	var transactions []*model.Transaction
	err := d.db.WithContext(ctx).Table(tableName).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&transactions).Error
	return transactions, err
}

// CountTransactionsByUser 统计用户的交易记录数量
func (d *financeDAO) CountTransactionsByUser(ctx context.Context, userID int64) (int64, error) {
	tableName := getTransactionTableName(userID)
	var count int64
	err := d.db.WithContext(ctx).Table(tableName).
		Where("user_id = ?", userID).
		Count(&count).Error
	return count, err
}

// CreateCommission 创建佣金记录
func (d *financeDAO) CreateCommission(ctx context.Context, commission *model.Commission) error {
	return d.db.WithContext(ctx).Create(commission).Error
}

// UpdateCommission 更新佣金记录
func (d *financeDAO) UpdateCommission(ctx context.Context, commission *model.Commission) error {
	return d.db.WithContext(ctx).Save(commission).Error
}

// FindCommissionByID 根据ID查找佣金记录
func (d *financeDAO) FindCommissionByID(ctx context.Context, id int64) (*model.Commission, error) {
	var commission model.Commission
	err := d.db.WithContext(ctx).First(&commission, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &commission, nil
}

// ListCommissions 查询佣金记录列表
func (d *financeDAO) ListCommissions(ctx context.Context, offset, limit int) ([]*model.Commission, error) {
	var commissions []*model.Commission
	err := d.db.WithContext(ctx).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&commissions).Error
	return commissions, err
}

// CountCommissions 统计佣金记录数量
func (d *financeDAO) CountCommissions(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Commission{}).Count(&count).Error
	return count, err
}

// GetTotalIncomeByDateRange 获取指定时间范围内的总收入
func (d *financeDAO) GetTotalIncomeByDateRange(ctx context.Context, startDate, endDate time.Time) (float64, error) {
	var total float64
	
	// 遍历所有交易分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("transaction_%d", i)
		var subtotal float64
		err := d.db.WithContext(ctx).Table(tableName).
			Select("COALESCE(SUM(amount), 0)").
			Where("type IN (1, 6) AND status = 1 AND created_at >= ? AND created_at < ?", startDate, endDate).
			Scan(&subtotal).Error
		if err != nil {
			return 0, err
		}
		total += subtotal
	}
	
	return total, nil
}

// GetTotalExpenseByDateRange 获取指定时间范围内的总支出
func (d *financeDAO) GetTotalExpenseByDateRange(ctx context.Context, startDate, endDate time.Time) (float64, error) {
	var total float64
	
	// 遍历所有交易分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("transaction_%d", i)
		var subtotal float64
		err := d.db.WithContext(ctx).Table(tableName).
			Select("COALESCE(SUM(amount), 0)").
			Where("type IN (2, 3) AND status = 1 AND created_at >= ? AND created_at < ?", startDate, endDate).
			Scan(&subtotal).Error
		if err != nil {
			return 0, err
		}
		total += subtotal
	}
	
	return total, nil
}

// GetTransactionCountByDateRange 获取指定时间范围内的交易数量
func (d *financeDAO) GetTransactionCountByDateRange(ctx context.Context, startDate, endDate time.Time) (int64, error) {
	var total int64
	
	// 遍历所有交易分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("transaction_%d", i)
		var count int64
		err := d.db.WithContext(ctx).Table(tableName).
			Where("status = 1 AND created_at >= ? AND created_at < ?", startDate, endDate).
			Count(&count).Error
		if err != nil {
			return 0, err
		}
		total += count
	}
	
	return total, nil
}

// GetUserTotalIncomeByDateRange 获取用户指定时间范围内的总收入
func (d *financeDAO) GetUserTotalIncomeByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (float64, error) {
	tableName := getTransactionTableName(userID)
	var total float64
	err := d.db.WithContext(ctx).Table(tableName).
		Select("COALESCE(SUM(amount), 0)").
		Where("user_id = ? AND type IN (1, 6) AND status = 1 AND created_at >= ? AND created_at < ?", userID, startDate, endDate).
		Scan(&total).Error
	return total, err
}

// GetUserTotalExpenseByDateRange 获取用户指定时间范围内的总支出
func (d *financeDAO) GetUserTotalExpenseByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (float64, error) {
	tableName := getTransactionTableName(userID)
	var total float64
	err := d.db.WithContext(ctx).Table(tableName).
		Select("COALESCE(SUM(amount), 0)").
		Where("user_id = ? AND type IN (2, 3) AND status = 1 AND created_at >= ? AND created_at < ?", userID, startDate, endDate).
		Scan(&total).Error
	return total, err
}

// GetUserTransactionCountByDateRange 获取用户指定时间范围内的交易数量
func (d *financeDAO) GetUserTransactionCountByDateRange(ctx context.Context, userID int64, startDate, endDate time.Time) (int64, error) {
	tableName := getTransactionTableName(userID)
	var count int64
	err := d.db.WithContext(ctx).Table(tableName).
		Where("user_id = ? AND status = 1 AND created_at >= ? AND created_at < ?", userID, startDate, endDate).
		Count(&count).Error
	return count, err
}

// getTransactionTableName 获取交易表名
func getTransactionTableName(userID int64) string {
	return fmt.Sprintf("transaction_%d", userID%10)
}
