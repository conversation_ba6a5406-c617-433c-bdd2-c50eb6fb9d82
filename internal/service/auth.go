package service

import (
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/spf13/viper"
)

var (
	ErrInvalidCredentials = errors.New("用户名或密码错误")
)

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   int64   `json:"user_id"`
	Username string  `json:"username"`
	UserType int8    `json:"user_type"`
	Roles    []int64 `json:"roles"`
	jwt.RegisteredClaims
}

// LoginResponse 登录响应
type LoginResponse struct {
	User  *model.UserWithRoles `json:"user"`
	Token string               `json:"token"`
}

// AuthService 认证服务接口
type AuthService interface {
	Login(ctx context.Context, username, password string) (*LoginResponse, error)
	RefreshToken(ctx context.Context, token string) (string, error)
	Logout(ctx context.Context, token string) error
	GenerateToken(userID int64, username string, userType int8, roles []int64) (string, error)
	ParseToken(tokenString string) (*JWTClaims, error)
}

// authService 认证服务实现
type authService struct {
	userService UserService
}

// NewAuthService 创建认证服务实例
func NewAuthService() AuthService {
	return &authService{
		userService: NewUserService(),
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, username, password string) (*LoginResponse, error) {
	// 验证用户凭据
	userWithRoles, err := s.userService.Login(ctx, username, password)
	if err != nil {
		if err == ErrUserNotFound || err == ErrInvalidPassword {
			return nil, ErrInvalidCredentials
		}
		return nil, err
	}

	// 检查用户状态
	if userWithRoles.Status != 1 {
		return nil, errors.New("用户已被禁用")
	}

	// 提取角色ID
	roleIDs := make([]int64, len(userWithRoles.Roles))
	for i, role := range userWithRoles.Roles {
		roleIDs[i] = role.ID
	}

	// 生成JWT令牌
	token, err := s.GenerateToken(userWithRoles.ID, userWithRoles.Username, userWithRoles.UserType, roleIDs)
	if err != nil {
		return nil, err
	}

	return &LoginResponse{
		User:  userWithRoles,
		Token: token,
	}, nil
}

// GenerateToken 生成JWT令牌
func (s *authService) GenerateToken(userID int64, username string, userType int8, roles []int64) (string, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	expireHours := viper.GetInt("jwt.expireHours")
	if expireHours == 0 {
		expireHours = 24
	}

	claims := JWTClaims{
		UserID:   userID,
		Username: username,
		UserType: userType,
		Roles:    roles,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "flower-auction",
			Subject:   username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

// ParseToken 解析JWT令牌
func (s *authService) ParseToken(tokenString string) (*JWTClaims, error) {
	secret := viper.GetString("jwt.secret")
	if secret == "" {
		secret = "flower_auction_secret_key"
	}

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("token无效")
}

// RefreshToken 刷新令牌
func (s *authService) RefreshToken(ctx context.Context, token string) (string, error) {
	claims, err := s.ParseToken(token)
	if err != nil {
		return "", err
	}

	// 检查token是否即将过期（1小时内）
	if time.Until(claims.ExpiresAt.Time) > time.Hour {
		return token, nil // 不需要刷新
	}

	// 生成新token
	return s.GenerateToken(claims.UserID, claims.Username, claims.UserType, claims.Roles)
}

// Logout 用户登出
func (s *authService) Logout(ctx context.Context, token string) error {
	// 这里可以实现令牌黑名单机制
	// 目前简单返回成功
	return nil
}
