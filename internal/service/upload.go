package service

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/spf13/viper"
)

var (
	ErrInvalidFileType = errors.New("不支持的文件类型")
	ErrFileTooLarge    = errors.New("文件大小超限")
	ErrUploadFailed    = errors.New("文件上传失败")
)

// UploadService 文件上传服务接口
type UploadService interface {
	// 图片上传
	UploadImage(ctx context.Context, file *multipart.FileHeader, folder string) (string, error)
	UploadImages(ctx context.Context, files []*multipart.FileHeader, folder string) ([]string, error)

	// 文档上传
	UploadDocument(ctx context.Context, file *multipart.FileHeader, folder string) (string, error)

	// 删除文件
	DeleteFile(ctx context.Context, fileURL string) error

	// 获取文件信息
	GetFileInfo(ctx context.Context, fileURL string) (map[string]interface{}, error)
}

// uploadService 文件上传服务实现
type uploadService struct {
	ossEndpoint   string
	ossBucket     string
	ossAccessKey  string
	ossSecretKey  string
	ossBaseURL    string
	maxImageSize  int64
	maxDocSize    int64
	allowedImages []string
	allowedDocs   []string
}

// NewUploadService 创建文件上传服务实例
func NewUploadService() UploadService {
	return &uploadService{
		ossEndpoint:   viper.GetString("oss.endpoint"),
		ossBucket:     viper.GetString("oss.bucket"),
		ossAccessKey:  viper.GetString("oss.accessKey"),
		ossSecretKey:  viper.GetString("oss.secretKey"),
		ossBaseURL:    viper.GetString("oss.baseURL"),
		maxImageSize:  viper.GetInt64("upload.maxImageSize"),
		maxDocSize:    viper.GetInt64("upload.maxDocSize"),
		allowedImages: viper.GetStringSlice("upload.allowedImages"),
		allowedDocs:   viper.GetStringSlice("upload.allowedDocs"),
	}
}

// UploadImage 上传图片
func (s *uploadService) UploadImage(ctx context.Context, file *multipart.FileHeader, folder string) (string, error) {
	// 验证文件类型
	if !s.isValidImageType(file.Filename) {
		return "", ErrInvalidFileType
	}

	// 验证文件大小
	maxSize := s.maxImageSize
	if maxSize == 0 {
		maxSize = 5 * 1024 * 1024 // 默认5MB
	}
	if file.Size > maxSize {
		return "", ErrFileTooLarge
	}

	// 生成文件名
	fileName := s.generateFileName(file.Filename)
	objectKey := fmt.Sprintf("%s/%s", folder, fileName)

	// 上传文件
	fileURL, err := s.uploadToOSS(ctx, file, objectKey)
	if err != nil {
		return "", err
	}

	return fileURL, nil
}

// UploadImages 批量上传图片
func (s *uploadService) UploadImages(ctx context.Context, files []*multipart.FileHeader, folder string) ([]string, error) {
	var urls []string
	var errors []error

	for _, file := range files {
		url, err := s.UploadImage(ctx, file, folder)
		if err != nil {
			errors = append(errors, err)
			urls = append(urls, "")
		} else {
			errors = append(errors, nil)
			urls = append(urls, url)
		}
	}

	// 如果有任何错误，返回第一个错误
	for _, err := range errors {
		if err != nil {
			return urls, err
		}
	}

	return urls, nil
}

// UploadDocument 上传文档
func (s *uploadService) UploadDocument(ctx context.Context, file *multipart.FileHeader, folder string) (string, error) {
	// 验证文件类型
	if !s.isValidDocType(file.Filename) {
		return "", ErrInvalidFileType
	}

	// 验证文件大小
	maxSize := s.maxDocSize
	if maxSize == 0 {
		maxSize = 10 * 1024 * 1024 // 默认10MB
	}
	if file.Size > maxSize {
		return "", ErrFileTooLarge
	}

	// 生成文件名
	fileName := s.generateFileName(file.Filename)
	objectKey := fmt.Sprintf("%s/%s", folder, fileName)

	// 上传文件
	fileURL, err := s.uploadToOSS(ctx, file, objectKey)
	if err != nil {
		return "", err
	}

	return fileURL, nil
}

// DeleteFile 删除文件
func (s *uploadService) DeleteFile(ctx context.Context, fileURL string) error {
	// 从URL中提取对象键
	objectKey := s.extractObjectKey(fileURL)
	if objectKey == "" {
		return errors.New("无效的文件URL")
	}

	// 这里应该调用OSS SDK删除文件
	// 由于没有实际的OSS配置，这里只是模拟
	// 实际实现需要使用阿里云OSS SDK

	return nil
}

// GetFileInfo 获取文件信息
func (s *uploadService) GetFileInfo(ctx context.Context, fileURL string) (map[string]interface{}, error) {
	objectKey := s.extractObjectKey(fileURL)
	if objectKey == "" {
		return nil, errors.New("无效的文件URL")
	}

	// 这里应该调用OSS SDK获取文件信息
	// 模拟返回文件信息
	info := map[string]interface{}{
		"url":          fileURL,
		"objectKey":    objectKey,
		"size":         0,
		"contentType":  "",
		"lastModified": time.Now(),
	}

	return info, nil
}

// isValidImageType 验证图片类型
func (s *uploadService) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	allowedTypes := s.allowedImages
	if len(allowedTypes) == 0 {
		allowedTypes = []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	}

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// isValidDocType 验证文档类型
func (s *uploadService) isValidDocType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))

	allowedTypes := s.allowedDocs
	if len(allowedTypes) == 0 {
		allowedTypes = []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt"}
	}

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// generateFileName 生成文件名
func (s *uploadService) generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)

	// 使用时间戳和MD5生成唯一文件名
	timestamp := time.Now().Unix()
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", name, timestamp)))

	return fmt.Sprintf("%x%s", hash, ext)
}

// uploadToOSS 上传文件到OSS
func (s *uploadService) uploadToOSS(ctx context.Context, file *multipart.FileHeader, objectKey string) (string, error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		return "", err
	}
	defer src.Close()

	// 这里应该使用阿里云OSS SDK上传文件
	// 由于没有实际的OSS配置，这里只是模拟
	// 实际实现示例：
	/*
		client, err := oss.New(s.ossEndpoint, s.ossAccessKey, s.ossSecretKey)
		if err != nil {
			return "", err
		}

		bucket, err := client.Bucket(s.ossBucket)
		if err != nil {
			return "", err
		}

		err = bucket.PutObject(objectKey, src)
		if err != nil {
			return "", err
		}
	*/

	// 模拟上传成功，返回文件URL
	baseURL := s.ossBaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("https://%s.%s", s.ossBucket, s.ossEndpoint)
	}

	fileURL := fmt.Sprintf("%s/%s", baseURL, objectKey)
	return fileURL, nil
}

// extractObjectKey 从URL中提取对象键
func (s *uploadService) extractObjectKey(fileURL string) string {
	baseURL := s.ossBaseURL
	if baseURL == "" {
		baseURL = fmt.Sprintf("https://%s.%s", s.ossBucket, s.ossEndpoint)
	}

	if strings.HasPrefix(fileURL, baseURL) {
		return strings.TrimPrefix(fileURL, baseURL+"/")
	}

	return ""
}

// LocalUploadService 本地文件上传服务（用于开发环境）
type LocalUploadService struct {
	uploadDir string
	baseURL   string
}

// NewLocalUploadService 创建本地文件上传服务
func NewLocalUploadService() UploadService {
	return &LocalUploadService{
		uploadDir: viper.GetString("upload.localDir"),
		baseURL:   viper.GetString("upload.baseURL"),
	}
}

// UploadImage 本地上传图片
func (s *LocalUploadService) UploadImage(ctx context.Context, file *multipart.FileHeader, folder string) (string, error) {
	// 验证文件类型
	if !s.isValidImageType(file.Filename) {
		return "", ErrInvalidFileType
	}

	// 验证文件大小
	if file.Size > 5*1024*1024 { // 5MB
		return "", ErrFileTooLarge
	}

	// 生成文件名
	fileName := s.generateFileName(file.Filename)

	// 构建文件路径
	relativePath := fmt.Sprintf("%s/%s", folder, fileName)

	// 这里应该保存文件到本地目录
	// 实际实现需要创建目录并保存文件

	// 返回文件URL
	fileURL := fmt.Sprintf("%s/%s", s.baseURL, relativePath)
	return fileURL, nil
}

// UploadImages 本地批量上传图片
func (s *LocalUploadService) UploadImages(ctx context.Context, files []*multipart.FileHeader, folder string) ([]string, error) {
	var urls []string
	for _, file := range files {
		url, err := s.UploadImage(ctx, file, folder)
		if err != nil {
			return urls, err
		}
		urls = append(urls, url)
	}
	return urls, nil
}

// UploadDocument 本地上传文档
func (s *LocalUploadService) UploadDocument(ctx context.Context, file *multipart.FileHeader, folder string) (string, error) {
	// 验证文件类型
	if !s.isValidDocType(file.Filename) {
		return "", ErrInvalidFileType
	}

	// 验证文件大小
	if file.Size > 10*1024*1024 { // 10MB
		return "", ErrFileTooLarge
	}

	// 生成文件名
	fileName := s.generateFileName(file.Filename)

	// 构建文件路径
	relativePath := fmt.Sprintf("%s/%s", folder, fileName)

	// 返回文件URL
	fileURL := fmt.Sprintf("%s/%s", s.baseURL, relativePath)
	return fileURL, nil
}

// DeleteFile 本地删除文件
func (s *LocalUploadService) DeleteFile(ctx context.Context, fileURL string) error {
	// 实际实现需要从本地文件系统删除文件
	return nil
}

// GetFileInfo 本地获取文件信息
func (s *LocalUploadService) GetFileInfo(ctx context.Context, fileURL string) (map[string]interface{}, error) {
	info := map[string]interface{}{
		"url":          fileURL,
		"size":         0,
		"contentType":  "",
		"lastModified": time.Now(),
	}
	return info, nil
}

// isValidImageType 验证图片类型
func (s *LocalUploadService) isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	allowedTypes := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// isValidDocType 验证文档类型
func (s *LocalUploadService) isValidDocType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	allowedTypes := []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt"}

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}

// generateFileName 生成文件名
func (s *LocalUploadService) generateFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)

	timestamp := time.Now().Unix()
	hash := md5.Sum([]byte(fmt.Sprintf("%s_%d", name, timestamp)))

	return fmt.Sprintf("%x%s", hash, ext)
}
