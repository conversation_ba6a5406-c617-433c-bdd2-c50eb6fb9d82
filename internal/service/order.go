package service

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrOrderNotFound   = errors.New("订单不存在")
	ErrPaymentNotFound = errors.New("支付记录不存在")
	ErrOrderPaid       = errors.New("订单已支付")
	ErrOrderNotPaid    = errors.New("订单未支付")
	ErrInvalidAmount   = errors.New("支付金额无效")
)

// OrderService 订单服务接口
type OrderService interface {
	// 订单管理
	CreateOrder(ctx context.Context, userID, auctionItemID int64, amount float64) (*model.Order, error)
	GetOrder(ctx context.Context, id int64) (*model.OrderDetail, error)
	GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderDetail, error)
	ListUserOrders(ctx context.Context, userID int64, page, size int) ([]*model.OrderDetail, int64, error)
	ListAllOrders(ctx context.Context, page, size int, status *int8) ([]*model.OrderDetail, int64, error)
	UpdateOrderStatus(ctx context.Context, id int64, status int8) error

	// 支付管理
	CreatePayment(ctx context.Context, orderID int64, amount float64, paymentMethod int8) (*model.Payment, error)
	GetPayment(ctx context.Context, paymentNo string) (*model.Payment, error)
	UpdatePaymentStatus(ctx context.Context, paymentNo string, status int8) error

	// 订单导出和统计
	ExportOrders(ctx context.Context, query *model.OrderExportQuery) ([]byte, error)
	GetOrderStatistics(ctx context.Context, query *model.OrderStatisticsQuery) (*model.OrderStatistics, error)
}

// orderService 订单服务实现
type orderService struct {
	orderDAO   dao.OrderDAO
	auctionDAO dao.AuctionDAO
	userDAO    dao.UserDAO
}

// NewOrderService 创建订单服务实例
func NewOrderService() OrderService {
	return &orderService{
		orderDAO:   dao.NewOrderDAO(),
		auctionDAO: dao.NewAuctionDAO(),
		userDAO:    dao.NewUserDAO(),
	}
}

// generateOrderNo 生成订单号
func generateOrderNo() string {
	return fmt.Sprintf("%s%d", time.Now().Format("20060102150405"), time.Now().UnixNano()%1000)
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(ctx context.Context, userID, auctionItemID int64, amount float64) (*model.Order, error) {
	// 检查拍卖商品是否存在
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, auctionItemID)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, ErrAuctionItemNotFound
	}

	// 检查商品是否已成交
	if item.Status != 2 {
		return nil, ErrInvalidOperation
	}

	// 检查是否是中标用户
	if item.WinnerID == nil || *item.WinnerID != userID {
		return nil, ErrInvalidOperation
	}

	// 创建订单
	order := &model.Order{
		OrderNo:       generateOrderNo(),
		UserID:        userID,
		AuctionItemID: auctionItemID,
		Amount:        amount,
		Status:        0, // 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreateOrder(ctx, order); err != nil {
		return nil, err
	}

	return order, nil
}

// GetOrder 获取订单信息
func (s *orderService) GetOrder(ctx context.Context, id int64) (*model.OrderDetail, error) {
	order, err := s.orderDAO.FindOrderByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	return s.getOrderDetail(ctx, order)
}

// GetOrderByNo 根据订单号获取订单信息
func (s *orderService) GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderDetail, error) {
	order, err := s.orderDAO.FindOrderByNo(ctx, orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	return s.getOrderDetail(ctx, order)
}

// getOrderDetail 获取订单详细信息
func (s *orderService) getOrderDetail(ctx context.Context, order *model.Order) (*model.OrderDetail, error) {
	// 获取拍卖商品信息
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, order.AuctionItemID)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	user, err := s.userDAO.FindByID(ctx, order.UserID)
	if err != nil {
		return nil, err
	}

	// 获取支付信息
	payment, err := s.orderDAO.FindPaymentByOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	return &model.OrderDetail{
		Order:       *order,
		AuctionItem: *item,
		User:        *user,
		Payment:     payment,
	}, nil
}

// ListUserOrders 查询用户订单列表
func (s *orderService) ListUserOrders(ctx context.Context, userID int64, page, size int) ([]*model.OrderDetail, int64, error) {
	offset := (page - 1) * size
	orders, err := s.orderDAO.ListOrdersByUser(ctx, userID, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.orderDAO.CountOrdersByUser(ctx, userID)
	if err != nil {
		return nil, 0, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))
	for _, order := range orders {
		detail, err := s.getOrderDetail(ctx, order)
		if err != nil {
			return nil, 0, err
		}
		orderDetails = append(orderDetails, detail)
	}

	return orderDetails, total, nil
}

// UpdateOrderStatus 更新订单状态
func (s *orderService) UpdateOrderStatus(ctx context.Context, id int64, status int8) error {
	order, err := s.orderDAO.FindOrderByID(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		return ErrOrderNotFound
	}

	now := time.Now()
	order.Status = status
	order.UpdatedAt = now

	// 根据状态设置相应的时间
	switch status {
	case 1: // 已支付
		order.PayTime = &now
	case 2: // 已发货
		order.ShipTime = &now
	case 3: // 已完成
		order.CompleteTime = &now
	}

	return s.orderDAO.UpdateOrder(ctx, order)
}

// CreatePayment 创建支付记录
func (s *orderService) CreatePayment(ctx context.Context, orderID int64, amount float64, paymentMethod int8) (*model.Payment, error) {
	// 检查订单是否存在
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 检查订单状态
	if order.Status != 0 {
		return nil, ErrOrderPaid
	}

	// 检查支付金额
	if amount != order.Amount {
		return nil, ErrInvalidAmount
	}

	// 创建支付记录
	payment := &model.Payment{
		OrderID:       orderID,
		PaymentNo:     generatePaymentNo("PAY"),
		Amount:        amount,
		PaymentMethod: paymentMethod,
		Status:        0, // 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreatePayment(ctx, payment); err != nil {
		return nil, err
	}

	return payment, nil
}

// GetPayment 获取支付记录
func (s *orderService) GetPayment(ctx context.Context, paymentNo string) (*model.Payment, error) {
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, ErrPaymentNotFound
	}
	return payment, nil
}

// UpdatePaymentStatus 更新支付状态
func (s *orderService) UpdatePaymentStatus(ctx context.Context, paymentNo string, status int8) error {
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	payment.Status = status
	payment.UpdatedAt = time.Now()

	// 如果支付成功，更新订单状态
	if status == 1 {
		if err := s.UpdateOrderStatus(ctx, payment.OrderID, 1); err != nil {
			return err
		}
	}

	return s.orderDAO.UpdatePayment(ctx, payment)
}

// ListAllOrders 查询所有订单列表（管理员功能）
func (s *orderService) ListAllOrders(ctx context.Context, page, size int, status *int8) ([]*model.OrderDetail, int64, error) {
	offset := (page - 1) * size
	orders, err := s.orderDAO.ListAllOrders(ctx, offset, size, status)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.orderDAO.CountAllOrders(ctx, status)
	if err != nil {
		return nil, 0, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))
	for _, order := range orders {
		detail, err := s.getOrderDetail(ctx, order)
		if err != nil {
			return nil, 0, err
		}
		orderDetails = append(orderDetails, detail)
	}

	return orderDetails, total, nil
}

// ExportOrders 导出订单
func (s *orderService) ExportOrders(ctx context.Context, query *model.OrderExportQuery) ([]byte, error) {
	// 获取订单数据
	orders, err := s.getOrdersForExport(ctx, query)
	if err != nil {
		return nil, err
	}

	// 根据格式生成文件
	switch query.Format {
	case "csv":
		return s.generateCSV(orders)
	case "excel":
		return s.generateExcel(orders)
	default:
		return s.generateCSV(orders) // 默认CSV格式
	}
}

// GetOrderStatistics 获取订单统计
func (s *orderService) GetOrderStatistics(ctx context.Context, query *model.OrderStatisticsQuery) (*model.OrderStatistics, error) {
	// 获取基础统计数据
	totalOrders, totalAmount, err := s.getBasicStats(ctx, query)
	if err != nil {
		return nil, err
	}

	// 计算平均金额
	avgAmount := float64(0)
	if totalOrders > 0 {
		avgAmount = totalAmount / float64(totalOrders)
	}

	// 获取状态统计
	statusStats, err := s.getStatusStats(ctx, query)
	if err != nil {
		return nil, err
	}

	// 获取时间序列数据
	timeSeriesData, err := s.getTimeSeriesData(ctx, query)
	if err != nil {
		return nil, err
	}

	// 获取用户排行
	topUsers, err := s.getTopUsers(ctx, query)
	if err != nil {
		return nil, err
	}

	return &model.OrderStatistics{
		TotalOrders:    totalOrders,
		TotalAmount:    totalAmount,
		AvgAmount:      avgAmount,
		StatusStats:    statusStats,
		TimeSeriesData: timeSeriesData,
		TopUsers:       topUsers,
	}, nil
}

// getOrdersForExport 获取用于导出的订单数据
func (s *orderService) getOrdersForExport(ctx context.Context, query *model.OrderExportQuery) ([]*model.OrderDetail, error) {
	// 这里简化实现，实际应该根据查询条件从数据库获取
	// 为了演示，我们获取所有订单然后过滤
	allOrders, _, err := s.ListAllOrders(ctx, 1, 10000, query.Status)
	if err != nil {
		return nil, err
	}

	var filteredOrders []*model.OrderDetail
	for _, order := range allOrders {
		// 时间过滤
		if order.CreatedAt.Before(query.StartDate) || order.CreatedAt.After(query.EndDate) {
			continue
		}

		// 用户过滤
		if query.UserID != nil && order.UserID != *query.UserID {
			continue
		}

		// 金额过滤
		if query.MinAmount != nil && order.Amount < *query.MinAmount {
			continue
		}
		if query.MaxAmount != nil && order.Amount > *query.MaxAmount {
			continue
		}

		filteredOrders = append(filteredOrders, order)
	}

	return filteredOrders, nil
}

// generateCSV 生成CSV格式数据
func (s *orderService) generateCSV(orders []*model.OrderDetail) ([]byte, error) {
	var csvData []string

	// CSV头部
	csvData = append(csvData, "订单号,用户ID,商品ID,金额,状态,创建时间")

	// CSV数据行
	for _, order := range orders {
		statusName := getOrderStatusName(order.Status)
		line := fmt.Sprintf("%s,%d,%d,%.2f,%s,%s",
			order.OrderNo,
			order.UserID,
			order.AuctionItemID,
			order.Amount,
			statusName,
			order.CreatedAt.Format("2006-01-02 15:04:05"))
		csvData = append(csvData, line)
	}

	return []byte(strings.Join(csvData, "\n")), nil
}

// generateExcel 生成Excel格式数据
func (s *orderService) generateExcel(orders []*model.OrderDetail) ([]byte, error) {
	// 这里简化实现，实际应该使用Excel库如excelize
	// 为了演示，我们返回CSV格式
	return s.generateCSV(orders)
}

// getBasicStats 获取基础统计数据
func (s *orderService) getBasicStats(ctx context.Context, query *model.OrderStatisticsQuery) (int64, float64, error) {
	// 简化实现，实际应该直接从数据库查询统计数据
	orders, _, err := s.ListAllOrders(ctx, 1, 10000, query.Status)
	if err != nil {
		return 0, 0, err
	}

	var totalOrders int64
	var totalAmount float64

	for _, order := range orders {
		// 时间过滤
		if order.CreatedAt.Before(query.StartDate) || order.CreatedAt.After(query.EndDate) {
			continue
		}

		// 用户过滤
		if query.UserID != nil && order.UserID != *query.UserID {
			continue
		}

		totalOrders++
		totalAmount += order.Amount
	}

	return totalOrders, totalAmount, nil
}

// getStatusStats 获取状态统计
func (s *orderService) getStatusStats(ctx context.Context, query *model.OrderStatisticsQuery) (map[string]int64, error) {
	orders, _, err := s.ListAllOrders(ctx, 1, 10000, nil) // 获取所有状态的订单
	if err != nil {
		return nil, err
	}

	statusStats := make(map[string]int64)
	statusStats["待支付"] = 0
	statusStats["已支付"] = 0
	statusStats["已发货"] = 0
	statusStats["已完成"] = 0
	statusStats["已取消"] = 0

	for _, order := range orders {
		// 时间过滤
		if order.CreatedAt.Before(query.StartDate) || order.CreatedAt.After(query.EndDate) {
			continue
		}

		// 用户过滤
		if query.UserID != nil && order.UserID != *query.UserID {
			continue
		}

		statusName := getOrderStatusName(order.Status)
		statusStats[statusName]++
	}

	return statusStats, nil
}

// getTimeSeriesData 获取时间序列数据
func (s *orderService) getTimeSeriesData(ctx context.Context, query *model.OrderStatisticsQuery) ([]*model.OrderTimeSeriesData, error) {
	orders, _, err := s.ListAllOrders(ctx, 1, 10000, query.Status)
	if err != nil {
		return nil, err
	}

	// 按日期分组统计
	dateStats := make(map[string]*model.OrderTimeSeriesData)

	for _, order := range orders {
		// 时间过滤
		if order.CreatedAt.Before(query.StartDate) || order.CreatedAt.After(query.EndDate) {
			continue
		}

		// 用户过滤
		if query.UserID != nil && order.UserID != *query.UserID {
			continue
		}

		dateKey := order.CreatedAt.Format("2006-01-02")
		if _, exists := dateStats[dateKey]; !exists {
			dateStats[dateKey] = &model.OrderTimeSeriesData{
				Date:        dateKey,
				OrderCount:  0,
				TotalAmount: 0,
			}
		}

		dateStats[dateKey].OrderCount++
		dateStats[dateKey].TotalAmount += order.Amount
	}

	// 转换为切片
	var result []*model.OrderTimeSeriesData
	for _, data := range dateStats {
		result = append(result, data)
	}

	return result, nil
}

// getTopUsers 获取用户排行
func (s *orderService) getTopUsers(ctx context.Context, query *model.OrderStatisticsQuery) ([]*model.OrderUserStats, error) {
	orders, _, err := s.ListAllOrders(ctx, 1, 10000, query.Status)
	if err != nil {
		return nil, err
	}

	// 按用户分组统计
	userStats := make(map[int64]*model.OrderUserStats)

	for _, order := range orders {
		// 时间过滤
		if order.CreatedAt.Before(query.StartDate) || order.CreatedAt.After(query.EndDate) {
			continue
		}

		if _, exists := userStats[order.UserID]; !exists {
			userStats[order.UserID] = &model.OrderUserStats{
				UserID:      order.UserID,
				Username:    order.User.Username,
				OrderCount:  0,
				TotalAmount: 0,
			}
		}

		userStats[order.UserID].OrderCount++
		userStats[order.UserID].TotalAmount += order.Amount
	}

	// 转换为切片并排序（按订单数量降序）
	var result []*model.OrderUserStats
	for _, stats := range userStats {
		result = append(result, stats)
	}

	// 简单排序，实际应该使用更高效的排序算法
	for i := 0; i < len(result)-1; i++ {
		for j := i + 1; j < len(result); j++ {
			if result[i].OrderCount < result[j].OrderCount {
				result[i], result[j] = result[j], result[i]
			}
		}
	}

	// 返回前10名
	if len(result) > 10 {
		result = result[:10]
	}

	return result, nil
}

// getOrderStatusName 获取订单状态名称
func getOrderStatusName(status int8) string {
	switch status {
	case 0:
		return "待支付"
	case 1:
		return "已支付"
	case 2:
		return "已发货"
	case 3:
		return "已完成"
	case 4:
		return "已取消"
	default:
		return "未知"
	}
}
