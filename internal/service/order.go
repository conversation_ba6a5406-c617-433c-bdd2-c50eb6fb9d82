package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrOrderNotFound   = errors.New("订单不存在")
	ErrPaymentNotFound = errors.New("支付记录不存在")
	ErrOrderPaid       = errors.New("订单已支付")
	ErrOrderNotPaid    = errors.New("订单未支付")
	ErrInvalidAmount   = errors.New("支付金额无效")
)

// OrderService 订单服务接口
type OrderService interface {
	// 订单管理
	CreateOrder(ctx context.Context, userID, auctionItemID int64, amount float64) (*model.Order, error)
	GetOrder(ctx context.Context, id int64) (*model.OrderDetail, error)
	GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderDetail, error)
	ListUserOrders(ctx context.Context, userID int64, page, size int) ([]*model.OrderDetail, int64, error)
	ListAllOrders(ctx context.Context, page, size int, status *int8) ([]*model.OrderDetail, int64, error)
	UpdateOrderStatus(ctx context.Context, id int64, status int8) error

	// 支付管理
	CreatePayment(ctx context.Context, orderID int64, amount float64, paymentMethod int8) (*model.Payment, error)
	GetPayment(ctx context.Context, paymentNo string) (*model.Payment, error)
	UpdatePaymentStatus(ctx context.Context, paymentNo string, status int8) error
}

// orderService 订单服务实现
type orderService struct {
	orderDAO   dao.OrderDAO
	auctionDAO dao.AuctionDAO
	userDAO    dao.UserDAO
}

// NewOrderService 创建订单服务实例
func NewOrderService() OrderService {
	return &orderService{
		orderDAO:   dao.NewOrderDAO(),
		auctionDAO: dao.NewAuctionDAO(),
		userDAO:    dao.NewUserDAO(),
	}
}

// generateOrderNo 生成订单号
func generateOrderNo() string {
	return fmt.Sprintf("%s%d", time.Now().Format("20060102150405"), time.Now().UnixNano()%1000)
}

// generatePaymentNo 生成支付流水号
func generatePaymentNo() string {
	return fmt.Sprintf("PAY%s%d", time.Now().Format("20060102150405"), time.Now().UnixNano()%1000)
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(ctx context.Context, userID, auctionItemID int64, amount float64) (*model.Order, error) {
	// 检查拍卖商品是否存在
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, auctionItemID)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, ErrAuctionItemNotFound
	}

	// 检查商品是否已成交
	if item.Status != 2 {
		return nil, ErrInvalidOperation
	}

	// 检查是否是中标用户
	if item.WinnerID == nil || *item.WinnerID != userID {
		return nil, ErrInvalidOperation
	}

	// 创建订单
	order := &model.Order{
		OrderNo:       generateOrderNo(),
		UserID:        userID,
		AuctionItemID: auctionItemID,
		Amount:        amount,
		Status:        0, // 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreateOrder(ctx, order); err != nil {
		return nil, err
	}

	return order, nil
}

// GetOrder 获取订单信息
func (s *orderService) GetOrder(ctx context.Context, id int64) (*model.OrderDetail, error) {
	order, err := s.orderDAO.FindOrderByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	return s.getOrderDetail(ctx, order)
}

// GetOrderByNo 根据订单号获取订单信息
func (s *orderService) GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderDetail, error) {
	order, err := s.orderDAO.FindOrderByNo(ctx, orderNo)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	return s.getOrderDetail(ctx, order)
}

// getOrderDetail 获取订单详细信息
func (s *orderService) getOrderDetail(ctx context.Context, order *model.Order) (*model.OrderDetail, error) {
	// 获取拍卖商品信息
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, order.AuctionItemID)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	user, err := s.userDAO.FindByID(ctx, order.UserID)
	if err != nil {
		return nil, err
	}

	// 获取支付信息
	payment, err := s.orderDAO.FindPaymentByOrderID(ctx, order.ID)
	if err != nil {
		return nil, err
	}

	return &model.OrderDetail{
		Order:       *order,
		AuctionItem: *item,
		User:        *user,
		Payment:     payment,
	}, nil
}

// ListUserOrders 查询用户订单列表
func (s *orderService) ListUserOrders(ctx context.Context, userID int64, page, size int) ([]*model.OrderDetail, int64, error) {
	offset := (page - 1) * size
	orders, err := s.orderDAO.ListOrdersByUser(ctx, userID, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.orderDAO.CountOrdersByUser(ctx, userID)
	if err != nil {
		return nil, 0, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))
	for _, order := range orders {
		detail, err := s.getOrderDetail(ctx, order)
		if err != nil {
			return nil, 0, err
		}
		orderDetails = append(orderDetails, detail)
	}

	return orderDetails, total, nil
}

// UpdateOrderStatus 更新订单状态
func (s *orderService) UpdateOrderStatus(ctx context.Context, id int64, status int8) error {
	order, err := s.orderDAO.FindOrderByID(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		return ErrOrderNotFound
	}

	now := time.Now()
	order.Status = status
	order.UpdatedAt = now

	// 根据状态设置相应的时间
	switch status {
	case 1: // 已支付
		order.PayTime = &now
	case 2: // 已发货
		order.ShipTime = &now
	case 3: // 已完成
		order.CompleteTime = &now
	}

	return s.orderDAO.UpdateOrder(ctx, order)
}

// CreatePayment 创建支付记录
func (s *orderService) CreatePayment(ctx context.Context, orderID int64, amount float64, paymentMethod int8) (*model.Payment, error) {
	// 检查订单是否存在
	order, err := s.orderDAO.FindOrderByID(ctx, orderID)
	if err != nil {
		return nil, err
	}
	if order == nil {
		return nil, ErrOrderNotFound
	}

	// 检查订单状态
	if order.Status != 0 {
		return nil, ErrOrderPaid
	}

	// 检查支付金额
	if amount != order.Amount {
		return nil, ErrInvalidAmount
	}

	// 创建支付记录
	payment := &model.Payment{
		OrderID:       orderID,
		PaymentNo:     generatePaymentNo(),
		Amount:        amount,
		PaymentMethod: paymentMethod,
		Status:        0, // 待支付
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	if err := s.orderDAO.CreatePayment(ctx, payment); err != nil {
		return nil, err
	}

	return payment, nil
}

// GetPayment 获取支付记录
func (s *orderService) GetPayment(ctx context.Context, paymentNo string) (*model.Payment, error) {
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, ErrPaymentNotFound
	}
	return payment, nil
}

// UpdatePaymentStatus 更新支付状态
func (s *orderService) UpdatePaymentStatus(ctx context.Context, paymentNo string, status int8) error {
	payment, err := s.orderDAO.FindPaymentByNo(ctx, paymentNo)
	if err != nil {
		return err
	}
	if payment == nil {
		return ErrPaymentNotFound
	}

	payment.Status = status
	payment.UpdatedAt = time.Now()

	// 如果支付成功，更新订单状态
	if status == 1 {
		if err := s.UpdateOrderStatus(ctx, payment.OrderID, 1); err != nil {
			return err
		}
	}

	return s.orderDAO.UpdatePayment(ctx, payment)
}

// ListAllOrders 查询所有订单列表（管理员功能）
func (s *orderService) ListAllOrders(ctx context.Context, page, size int, status *int8) ([]*model.OrderDetail, int64, error) {
	offset := (page - 1) * size
	orders, err := s.orderDAO.ListAllOrders(ctx, offset, size, status)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.orderDAO.CountAllOrders(ctx, status)
	if err != nil {
		return nil, 0, err
	}

	orderDetails := make([]*model.OrderDetail, 0, len(orders))
	for _, order := range orders {
		detail, err := s.getOrderDetail(ctx, order)
		if err != nil {
			return nil, 0, err
		}
		orderDetails = append(orderDetails, detail)
	}

	return orderDetails, total, nil
}
