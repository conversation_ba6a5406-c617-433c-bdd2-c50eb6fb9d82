package api

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/service"
)

// PaymentHandler 支付处理器
type PaymentHandler struct {
	paymentService service.PaymentService
}

// NewPaymentHandler 创建支付处理器实例
func NewPaymentHandler() *PaymentHandler {
	return &PaymentHandler{
		paymentService: service.NewPaymentService(),
	}
}

// RegisterRoutes 注册路由
func (h *PaymentHandler) RegisterRoutes(r *gin.Engine) {
	// 支付路由组
	payment := r.Group("/api/v1/payment")
	payment.Use(middleware.JWTAuth())
	{
		payment.POST("/alipay", h.CreateAlipayOrder)            // 创建支付宝支付订单
		payment.POST("/wechat", h.CreateWechatOrder)            // 创建微信支付订单
		payment.POST("/bank", h.CreateBankOrder)                // 创建银行卡支付订单
		payment.GET("/status/:paymentNo", h.QueryPaymentStatus) // 查询支付状态
		payment.POST("/refund", h.RefundPayment)                // 申请退款
	}

	// 支付回调路由（不需要认证）
	notify := r.Group("/api/v1/notify")
	{
		notify.POST("/alipay", h.AlipayNotify) // 支付宝支付回调
		notify.POST("/wechat", h.WechatNotify) // 微信支付回调
	}
}

// CreateAlipayOrderRequest 创建支付宝支付订单请求
type CreateAlipayOrderRequest struct {
	OrderID int64   `json:"orderId" binding:"required"`
	Amount  float64 `json:"amount" binding:"required,gt=0"`
	Subject string  `json:"subject" binding:"required"`
}

// CreateAlipayOrder 创建支付宝支付订单
func (h *PaymentHandler) CreateAlipayOrder(c *gin.Context) {
	var req CreateAlipayOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateAlipayOrder(c.Request.Context(), req.OrderID, req.Amount, req.Subject)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// CreateWechatOrderRequest 创建微信支付订单请求
type CreateWechatOrderRequest struct {
	OrderID int64   `json:"orderId" binding:"required"`
	Amount  float64 `json:"amount" binding:"required,gt=0"`
	Subject string  `json:"subject" binding:"required"`
}

// CreateWechatOrder 创建微信支付订单
func (h *PaymentHandler) CreateWechatOrder(c *gin.Context) {
	var req CreateWechatOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateWechatOrder(c.Request.Context(), req.OrderID, req.Amount, req.Subject)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// CreateBankOrderRequest 创建银行卡支付订单请求
type CreateBankOrderRequest struct {
	OrderID  int64   `json:"orderId" binding:"required"`
	Amount   float64 `json:"amount" binding:"required,gt=0"`
	BankCard string  `json:"bankCard" binding:"required"`
}

// CreateBankOrder 创建银行卡支付订单
func (h *PaymentHandler) CreateBankOrder(c *gin.Context) {
	var req CreateBankOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	paymentOrder, err := h.paymentService.CreateBankOrder(c.Request.Context(), req.OrderID, req.Amount, req.BankCard)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		if err == service.ErrInvalidOperation {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "订单状态不允许支付"})
			return
		}
		if err == service.ErrInvalidAmount {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付金额与订单金额不符"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, paymentOrder)
}

// QueryPaymentStatus 查询支付状态
func (h *PaymentHandler) QueryPaymentStatus(c *gin.Context) {
	paymentNo := strings.TrimSpace(c.Param("paymentNo"))
	if paymentNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "支付流水号不能为空"})
		return
	}

	status, err := h.paymentService.QueryPaymentStatus(c.Request.Context(), paymentNo)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, status)
}

// RefundPaymentRequest 申请退款请求
type RefundPaymentRequest struct {
	PaymentNo    string  `json:"paymentNo" binding:"required"`
	RefundAmount float64 `json:"refundAmount" binding:"required,gt=0"`
	Reason       string  `json:"reason" binding:"required"`
}

// RefundPayment 申请退款
func (h *PaymentHandler) RefundPayment(c *gin.Context) {
	var req RefundPaymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	refund, err := h.paymentService.RefundPayment(c.Request.Context(), req.PaymentNo, req.RefundAmount, req.Reason)
	if err != nil {
		if err == service.ErrPaymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "支付记录不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, refund)
}

// AlipayNotify 支付宝支付回调
func (h *PaymentHandler) AlipayNotify(c *gin.Context) {
	// 获取所有POST参数
	params := make(map[string]string)
	for key, values := range c.Request.PostForm {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	if err := h.paymentService.AlipayNotify(c.Request.Context(), params); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	c.String(http.StatusOK, "success")
}

// WechatNotify 微信支付回调
func (h *PaymentHandler) WechatNotify(c *gin.Context) {
	// 获取所有POST参数
	params := make(map[string]string)
	for key, values := range c.Request.PostForm {
		if len(values) > 0 {
			params[key] = values[0]
		}
	}

	if err := h.paymentService.WechatNotify(c.Request.Context(), params); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	c.String(http.StatusOK, "success")
}
