package main

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 运行测试
	code := m.Run()

	// 退出
	os.Exit(code)
}

func TestInitConfig(t *testing.T) {
	tests := []struct {
		name    string
		setup   func()
		wantErr bool
	}{
		{
			name: "成功初始化配置",
			setup: func() {
				// 创建临时配置文件
				viper.Reset()
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}

			err := initConfig()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestInitRouter(t *testing.T) {
	// 初始化配置
	viper.Set("server.mode", "test")

	router := initRouter()

	// 验证路由器不为空
	assert.NotNil(t, router)

	// 测试健康检查端点
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/health", nil)
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
	assert.Contains(t, w.Body.String(), "昆明花卉拍卖系统运行正常")
}

func TestGracefulShutdown(t *testing.T) {
	// 创建测试服务器
	router := initRouter()
	srv := &http.Server{
		Addr:    ":0", // 使用随机端口
		Handler: router,
	}

	// 启动服务器
	go func() {
		srv.ListenAndServe()
	}()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 测试优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := srv.Shutdown(ctx)
	assert.NoError(t, err)
}

func TestCleanup(t *testing.T) {
	// 测试清理函数不会panic
	assert.NotPanics(t, func() {
		cleanup()
	})
}

// 基准测试
func BenchmarkInitRouter(b *testing.B) {
	viper.Set("server.mode", "test")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		initRouter()
	}
}

func BenchmarkHealthCheck(b *testing.B) {
	router := initRouter()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/health", nil)
		router.ServeHTTP(w, req)
	}
}
