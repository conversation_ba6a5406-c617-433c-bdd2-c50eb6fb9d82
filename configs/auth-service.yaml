# 认证服务配置
server:
  port: 8081
  mode: debug
  read_timeout: 60
  write_timeout: 60

database:
  user:
    host: localhost
    port: 3306
    username: root
    password: password
    database: flower_auction_user
    charset: utf8mb4

jwt:
  secret: flower_auction_auth_secret_key
  expire_hours: 24

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

log:
  level: info
  filename: ./logs/auth-service.log
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true
